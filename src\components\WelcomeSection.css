.welcome-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rem 0;
  position: relative;
  overflow: hidden;
}

.welcome-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.welcome-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  position: relative;
  z-index: 1;
}

.welcome-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.welcome-title {
  font-size: 3rem;
  font-weight: 700;
  line-height: 1.2;
  margin: 0;
}

.welcome-description {
  font-size: 1.2rem;
  line-height: 1.6;
  opacity: 0.9;
  margin: 0;
}

.welcome-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  transition: transform 0.3s ease;
}

.feature:hover {
  transform: translateY(-2px);
}

.feature-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: #ffd700;
}

.feature span {
  font-weight: 500;
}

.welcome-image {
  position: relative;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.floating-card {
  position: absolute;
  background: white;
  color: #333;
  padding: 1rem 1.5rem;
  border-radius: 16px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
  animation: float 6s ease-in-out infinite;
}

.floating-card svg {
  color: #667eea;
}

.card-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.card-2 {
  top: 50%;
  right: 20%;
  animation-delay: 2s;
}

.card-3 {
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@media (max-width: 1024px) {
  .welcome-container {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }
  
  .welcome-title {
    font-size: 2.5rem;
  }
  
  .welcome-image {
    height: 300px;
  }
}

@media (max-width: 768px) {
  .welcome-section {
    padding: 3rem 0;
  }
  
  .welcome-title {
    font-size: 2rem;
  }
  
  .welcome-description {
    font-size: 1.1rem;
  }
  
  .welcome-features {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .welcome-image {
    height: 250px;
  }
  
  .floating-card {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
}
