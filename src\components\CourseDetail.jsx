import { 
  ArrowLeft, 
  Clock, 
  Users, 
  Star, 
  Award, 
  ExternalLink, 
  Calendar,
  User,
  Globe,
  BarChart3
} from 'lucide-react';
import './CourseDetail.css';

const CourseDetail = ({ course, onBack }) => {
  if (!course) return null;

  const handleEnrollClick = () => {
    window.open(course.enrollmentUrl, '_blank');
  };

  const getLevelColor = (level) => {
    switch (level) {
      case 'Débutant': return '#4CAF50';
      case 'Intermédiaire': return '#FF9800';
      case 'Avancé': return '#F44336';
      default: return '#2196F3';
    }
  };

  return (
    <div className="course-detail">
      <div className="course-detail-container">
        <button className="back-button" onClick={onBack}>
          <ArrowLeft size={20} />
          Retour aux cours
        </button>

        <div className="course-detail-header">
          <div className="course-detail-image">
            <img src={course.imageUrl} alt={course.title} />
            <div className="course-level-badge" style={{ backgroundColor: getLevelColor(course.level) }}>
              {course.level}
            </div>
            {course.certificate && (
              <div className="certificate-badge">
                <Award size={24} />
                <span>Certificat inclus</span>
              </div>
            )}
          </div>

          <div className="course-detail-info">
            <h1 className="course-detail-title">{course.title}</h1>
            <p className="course-platform">{course.platform}</p>
            
            <div className="course-stats">
              <div className="stat">
                <Star className="stat-icon" />
                <span>{course.rating}/5</span>
              </div>
              <div className="stat">
                <Users className="stat-icon" />
                <span>{course.students.toLocaleString()} étudiants</span>
              </div>
              <div className="stat">
                <Clock className="stat-icon" />
                <span>{course.duration}</span>
              </div>
            </div>

            <button className="enroll-button-large" onClick={handleEnrollClick}>
              <ExternalLink size={20} />
              S'inscrire maintenant
            </button>
          </div>
        </div>

        <div className="course-detail-content">
          <div className="course-main-content">
            <section className="course-section">
              <h2>Description</h2>
              <p>{course.description}</p>
            </section>

            <section className="course-section">
              <h2>Compétences acquises</h2>
              <div className="skills-grid">
                {course.skills.map((skill, index) => (
                  <span key={index} className="skill-badge">{skill}</span>
                ))}
              </div>
            </section>

            <section className="course-section">
              <h2>Langues disponibles</h2>
              <div className="language-info">
                <div className="language-item">
                  <Globe size={16} />
                  <span><strong>Langue principale:</strong> {course.language}</span>
                </div>
                {course.subtitles.length > 0 && (
                  <div className="language-item">
                    <Globe size={16} />
                    <span><strong>Sous-titres:</strong> {course.subtitles.join(', ')}</span>
                  </div>
                )}
              </div>
            </section>
          </div>

          <div className="course-sidebar">
            <div className="course-info-card">
              <h3>Informations du cours</h3>
              
              <div className="info-item">
                <User size={16} />
                <div>
                  <strong>Instructeur</strong>
                  <span>{course.instructor}</span>
                </div>
              </div>

              <div className="info-item">
                <Calendar size={16} />
                <div>
                  <strong>Date de début</strong>
                  <span>{course.startDate}</span>
                </div>
              </div>

              <div className="info-item">
                <Clock size={16} />
                <div>
                  <strong>Effort requis</strong>
                  <span>{course.effort}</span>
                </div>
              </div>

              <div className="info-item">
                <BarChart3 size={16} />
                <div>
                  <strong>Niveau</strong>
                  <span>{course.level}</span>
                </div>
              </div>

              <div className="info-item">
                <Globe size={16} />
                <div>
                  <strong>Langue</strong>
                  <span>{course.language}</span>
                </div>
              </div>

              {course.certificate && (
                <div className="certificate-info">
                  <Award size={16} />
                  <span>Certificat gratuit disponible</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseDetail;
