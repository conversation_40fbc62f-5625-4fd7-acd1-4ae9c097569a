import { Filter, ChevronDown } from 'lucide-react';
import { categories, levels, platforms } from '../data/courses';
import './FilterBar.css';

const FilterBar = ({ 
  selectedCategory, 
  selectedLevel, 
  selectedPlatform, 
  onCategoryChange, 
  onLevelChange, 
  onPlatformChange,
  courseCount 
}) => {
  return (
    <div className="filter-bar">
      <div className="filter-container">
        <div className="filter-header">
          <div className="filter-title">
            <Filter size={20} />
            <span>Filtres</span>
          </div>
          <div className="course-count">
            {courseCount} cours trouvé{courseCount > 1 ? 's' : ''}
          </div>
        </div>
        
        <div className="filter-controls">
          <div className="filter-group">
            <label htmlFor="category-select">Catégorie</label>
            <div className="select-wrapper">
              <select
                id="category-select"
                value={selectedCategory}
                onChange={(e) => onCategoryChange(e.target.value)}
                className="filter-select"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
              <ChevronDown className="select-icon" />
            </div>
          </div>
          
          <div className="filter-group">
            <label htmlFor="level-select">Niveau</label>
            <div className="select-wrapper">
              <select
                id="level-select"
                value={selectedLevel}
                onChange={(e) => onLevelChange(e.target.value)}
                className="filter-select"
              >
                {levels.map(level => (
                  <option key={level} value={level}>
                    {level}
                  </option>
                ))}
              </select>
              <ChevronDown className="select-icon" />
            </div>
          </div>
          
          <div className="filter-group">
            <label htmlFor="platform-select">Plateforme</label>
            <div className="select-wrapper">
              <select
                id="platform-select"
                value={selectedPlatform}
                onChange={(e) => onPlatformChange(e.target.value)}
                className="filter-select"
              >
                {platforms.map(platform => (
                  <option key={platform} value={platform}>
                    {platform}
                  </option>
                ))}
              </select>
              <ChevronDown className="select-icon" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilterBar;
