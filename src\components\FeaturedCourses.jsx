import { Star, TrendingUp, Award } from 'lucide-react';
import CourseCard from './CourseCard';
import { courses } from '../data/courses';
import './FeaturedCourses.css';

const FeaturedCourses = ({ onCourseClick }) => {
  // Sélectionner les cours les mieux notés et les plus populaires
  const featuredCourses = courses
    .filter(course => course.rating >= 4.7 || course.students >= 200000)
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 6);

  return (
    <section className="featured-courses">
      <div className="featured-container">
        <div className="featured-header">
          <div className="featured-title">
            <TrendingUp className="featured-icon" />
            <h2>Cours populaires et recommandés</h2>
          </div>
          <p className="featured-description">
            Découvrez les cours les mieux notés et les plus suivis par notre communauté
          </p>
        </div>

        <div className="featured-stats">
          <div className="stat-badge">
            <Star className="stat-badge-icon" />
            <span>Note moyenne: 4.7+</span>
          </div>
          <div className="stat-badge">
            <Award className="stat-badge-icon" />
            <span>100% Certifiés</span>
          </div>
        </div>

        <div className="featured-grid">
          {featuredCourses.map(course => (
            <CourseCard
              key={course.id}
              course={course}
              onClick={() => onCourseClick(course)}
            />
          ))}
        </div>

        <div className="featured-cta">
          <p>
            Vous cherchez quelque chose de spécifique ? 
            <span className="cta-highlight"> Utilisez nos filtres pour trouver le cours parfait !</span>
          </p>
        </div>
      </div>
    </section>
  );
};

export default FeaturedCourses;
