.filter-bar {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-bottom: 1px solid #e0e0e0;
  padding: 2rem 0;
  margin-bottom: 3rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.filter-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.filter-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #333;
  font-size: 1.1rem;
}

.filter-title svg {
  color: #667eea;
}

.course-count {
  color: #667eea;
  font-size: 0.95rem;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea15 0%, #764ba215 100%);
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  border: 1px solid #667eea30;
}

.filter-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.select-wrapper {
  position: relative;
}

.filter-select {
  width: 100%;
  padding: 1rem 3rem 1rem 1.5rem;
  border: 2px solid #e0e0e0;
  border-radius: 15px;
  background: white;
  font-size: 0.95rem;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.15), 0 4px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.filter-select:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.select-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1rem;
  height: 1rem;
  color: #666;
  pointer-events: none;
}

@media (max-width: 768px) {
  .filter-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .filter-controls {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}
