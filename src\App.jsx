import { useState, useMemo } from 'react';
import Header from './components/Header';
import WelcomeSection from './components/WelcomeSection';
import FeaturedCourses from './components/FeaturedCourses';
import CategoryShowcase from './components/CategoryShowcase';
import FilterBar from './components/FilterBar';
import CourseList from './components/CourseList';
import CourseDetail from './components/CourseDetail';
import Footer from './components/Footer';
import { courses } from './data/courses';
import './App.css';

function App() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('Tous');
  const [selectedLevel, setSelectedLevel] = useState('Tous');
  const [selectedPlatform, setSelectedPlatform] = useState('Tous');
  const [selectedCourse, setSelectedCourse] = useState(null);

  // Filtrage des cours
  const filteredCourses = useMemo(() => {
    return courses.filter(course => {
      const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           course.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           course.skills.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesCategory = selectedCategory === 'Tous' || course.category === selectedCategory;
      const matchesLevel = selectedLevel === 'Tous' || course.level === selectedLevel;
      const matchesPlatform = selectedPlatform === 'Tous' || course.platform.includes(selectedPlatform);

      return matchesSearch && matchesCategory && matchesLevel && matchesPlatform;
    });
  }, [searchTerm, selectedCategory, selectedLevel, selectedPlatform]);

  const handleCourseClick = (course) => {
    setSelectedCourse(course);
  };

  const handleBackToCourses = () => {
    setSelectedCourse(null);
  };

  // Vérifier si des filtres sont appliqués
  const hasActiveFilters = searchTerm ||
                          selectedCategory !== 'Tous' ||
                          selectedLevel !== 'Tous' ||
                          selectedPlatform !== 'Tous';

  if (selectedCourse) {
    return (
      <CourseDetail
        course={selectedCourse}
        onBack={handleBackToCourses}
      />
    );
  }

  return (
    <div className="app">
      <Header
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
      />

      {!hasActiveFilters && <WelcomeSection />}
      {!hasActiveFilters && <FeaturedCourses onCourseClick={handleCourseClick} />}
      {!hasActiveFilters && <CategoryShowcase onCategorySelect={setSelectedCategory} />}

      <FilterBar
        selectedCategory={selectedCategory}
        selectedLevel={selectedLevel}
        selectedPlatform={selectedPlatform}
        onCategoryChange={setSelectedCategory}
        onLevelChange={setSelectedLevel}
        onPlatformChange={setSelectedPlatform}
        courseCount={filteredCourses.length}
      />

      <CourseList
        courses={filteredCourses}
        onCourseClick={handleCourseClick}
      />

      <Footer />
    </div>
  );
}

export default App;
