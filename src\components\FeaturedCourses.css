.featured-courses {
  background: white;
  padding: 4rem 0;
  margin: 2rem 0;
}

.featured-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.featured-header {
  text-align: center;
  margin-bottom: 3rem;
}

.featured-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.featured-icon {
  width: 2rem;
  height: 2rem;
  color: #667eea;
}

.featured-title h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0;
}

.featured-description {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.featured-stats {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 3rem;
}

.stat-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.stat-badge-icon {
  width: 1.2rem;
  height: 1.2rem;
  color: #ffd700;
}

.featured-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.featured-cta {
  text-align: center;
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 16px;
  border-left: 4px solid #667eea;
}

.featured-cta p {
  font-size: 1.1rem;
  color: #333;
  margin: 0;
}

.cta-highlight {
  color: #667eea;
  font-weight: 600;
}

@media (max-width: 768px) {
  .featured-title {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .featured-title h2 {
    font-size: 2rem;
  }
  
  .featured-stats {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }
  
  .featured-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .featured-cta {
    padding: 1.5rem;
  }
}
