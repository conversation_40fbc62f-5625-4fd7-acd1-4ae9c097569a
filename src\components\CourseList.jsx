import CourseCard from './CourseCard';
import './CourseList.css';

const CourseList = ({ courses, onCourseClick }) => {
  if (courses.length === 0) {
    return (
      <div className="no-courses">
        <div className="no-courses-content">
          <h3>Aucun cours trouvé</h3>
          <p>Essayez de modifier vos critères de recherche ou vos filtres.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="course-list">
      <div className="course-grid">
        {courses.map(course => (
          <CourseCard
            key={course.id}
            course={course}
            onClick={() => onCourseClick(course)}
          />
        ))}
      </div>
    </div>
  );
};

export default CourseList;
