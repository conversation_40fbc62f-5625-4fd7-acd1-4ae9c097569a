import { Search, BookO<PERSON>, Award } from 'lucide-react';
import './Header.css';

const Header = ({ searchTerm, onSearchChange }) => {
  return (
    <header className="header">
      <div className="header-container">
        <div className="header-brand">
          <BookOpen className="brand-icon" />
          <h1 className="brand-title">CoursLibre</h1>
          <span className="brand-subtitle">Cours gratuits avec certificat</span>
        </div>
        
        <div className="header-search">
          <div className="search-container">
            <Search className="search-icon" />
            <input
              type="text"
              placeholder="Rechercher un cours..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="search-input"
            />
          </div>
        </div>

        <div className="header-stats">
          <div className="stat-item">
            <Award className="stat-icon" />
            <span className="stat-text">100% Gratuit</span>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
