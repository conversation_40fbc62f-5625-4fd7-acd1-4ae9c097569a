import { BookO<PERSON>, Users, Award, Globe } from 'lucide-react';
import { courses } from '../data/courses';
import './StatsSection.css';

const StatsSection = () => {
  const totalCourses = courses.length;
  const totalStudents = courses.reduce((sum, course) => sum + course.students, 0);
  const certificateCourses = courses.filter(course => course.certificate).length;
  const uniquePlatforms = [...new Set(courses.map(course => course.platform.split(' ')[0]))].length;

  const stats = [
    {
      icon: BookOpen,
      value: totalCourses,
      label: 'Cours disponibles',
      color: '#667eea'
    },
    {
      icon: Users,
      value: totalStudents.toLocaleString(),
      label: 'Étudiants inscrits',
      color: '#4CAF50'
    },
    {
      icon: Award,
      value: certificateCourses,
      label: 'Certificats gratuits',
      color: '#ffd700'
    },
    {
      icon: Globe,
      value: uniquePlatforms,
      label: 'Plateformes partenaires',
      color: '#FF9800'
    }
  ];

  return (
    <section className="stats-section">
      <div className="stats-container">
        <div className="stats-header">
          <h2>CoursLibre en chiffres</h2>
          <p>Découvrez l'impact de notre plateforme d'apprentissage gratuit</p>
        </div>
        
        <div className="stats-grid">
          {stats.map((stat, index) => {
            const IconComponent = stat.icon;
            return (
              <div key={index} className="stat-card">
                <div className="stat-icon" style={{ backgroundColor: `${stat.color}20`, color: stat.color }}>
                  <IconComponent size={32} />
                </div>
                <div className="stat-content">
                  <div className="stat-value" style={{ color: stat.color }}>
                    {stat.value}
                  </div>
                  <div className="stat-label">{stat.label}</div>
                </div>
              </div>
            );
          })}
        </div>
        
        <div className="stats-description">
          <p>
            Tous nos cours sont <strong>100% gratuits</strong> et incluent des certificats 
            de completion reconnus par les plus grandes plateformes d'apprentissage en ligne.
          </p>
        </div>
      </div>
    </section>
  );
};

export default StatsSection;
