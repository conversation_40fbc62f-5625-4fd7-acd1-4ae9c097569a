import { <PERSON><PERSON><PERSON>, Heart, Github, Mail } from 'lucide-react';
import './Footer.css';

const Footer = () => {
  return (
    <footer className="footer">
      <div className="footer-container">
        <div className="footer-content">
          <div className="footer-section">
            <div className="footer-brand">
              <BookOpen className="footer-logo" />
              <h3>CoursLibre</h3>
            </div>
            <p className="footer-description">
              Votre plateforme de référence pour découvrir les meilleurs cours gratuits 
              avec certificat. Apprenez de nouvelles compétences sans dépenser un centime.
            </p>
          </div>
          
          <div className="footer-section">
            <h4>Catégories populaires</h4>
            <ul className="footer-links">
              <li><a href="#informatique">Informatique</a></li>
              <li><a href="#ia">Intelligence Artificielle</a></li>
              <li><a href="#web">Développement Web</a></li>
              <li><a href="#marketing">Marketing Digital</a></li>
              <li><a href="#data">Data Science</a></li>
              <li><a href="#design">Design</a></li>
            </ul>
          </div>
          
          <div className="footer-section">
            <h4>Plateformes partenaires</h4>
            <ul className="footer-links">
              <li><a href="https://www.edx.org" target="_blank" rel="noopener noreferrer">edX</a></li>
              <li><a href="https://www.coursera.org" target="_blank" rel="noopener noreferrer">Coursera</a></li>
              <li><a href="https://www.freecodecamp.org" target="_blank" rel="noopener noreferrer">freeCodeCamp</a></li>
              <li><a href="https://learndigital.withgoogle.com" target="_blank" rel="noopener noreferrer">Google Digital Garage</a></li>
              <li><a href="https://www.kaggle.com/learn" target="_blank" rel="noopener noreferrer">Kaggle Learn</a></li>
            </ul>
          </div>
          
          <div className="footer-section">
            <h4>Contact</h4>
            <div className="footer-contact">
              <a href="mailto:<EMAIL>" className="contact-link">
                <Mail size={16} />
                <EMAIL>
              </a>
              <a href="https://github.com" target="_blank" rel="noopener noreferrer" className="contact-link">
                <Github size={16} />
                GitHub
              </a>
            </div>
          </div>
        </div>
        
        <div className="footer-bottom">
          <p className="footer-copyright">
            © 2024 CoursLibre. Fait avec <Heart size={16} className="heart" /> pour l'éducation libre.
          </p>
          <div className="footer-legal">
            <a href="#privacy">Politique de confidentialité</a>
            <a href="#terms">Conditions d'utilisation</a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
