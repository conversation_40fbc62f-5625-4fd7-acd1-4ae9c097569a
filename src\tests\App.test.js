// Tests basiques pour vérifier le bon fonctionnement de l'application
// Note: Pour des tests plus complets, installer @testing-library/react

import { courses, categories, levels, platforms } from '../data/courses.js';

// Test 1: Vérifier que les données de cours sont valides
console.log('🧪 Test 1: Validation des données de cours');
const testCourseData = () => {
  const requiredFields = ['id', 'title', 'platform', 'category', 'duration', 'level', 'certificate', 'rating', 'students'];
  
  courses.forEach((course, index) => {
    requiredFields.forEach(field => {
      if (course[field] === undefined) {
        console.error(`❌ Cours ${index + 1}: Champ manquant '${field}'`);
        return false;
      }
    });
  });
  
  console.log(`✅ ${courses.length} cours validés avec succès`);
  return true;
};

// Test 2: Vérifier la cohérence des catégories
console.log('\n🧪 Test 2: Validation des catégories');
const testCategories = () => {
  const courseCategories = [...new Set(courses.map(course => course.category))];
  const definedCategories = categories.filter(cat => cat !== 'Tous');
  
  courseCategories.forEach(category => {
    if (!definedCategories.includes(category)) {
      console.error(`❌ Catégorie '${category}' non définie dans la liste des catégories`);
      return false;
    }
  });
  
  console.log(`✅ ${courseCategories.length} catégories validées`);
  return true;
};

// Test 3: Vérifier les niveaux
console.log('\n🧪 Test 3: Validation des niveaux');
const testLevels = () => {
  const courseLevels = [...new Set(courses.map(course => course.level))];
  const definedLevels = levels.filter(level => level !== 'Tous');
  
  courseLevels.forEach(level => {
    if (!definedLevels.includes(level)) {
      console.error(`❌ Niveau '${level}' non défini dans la liste des niveaux`);
      return false;
    }
  });
  
  console.log(`✅ ${courseLevels.length} niveaux validés`);
  return true;
};

// Test 4: Vérifier les URLs
console.log('\n🧪 Test 4: Validation des URLs');
const testUrls = () => {
  const urlPattern = /^https?:\/\/.+/;
  
  courses.forEach((course, index) => {
    if (!urlPattern.test(course.enrollmentUrl)) {
      console.error(`❌ Cours ${index + 1}: URL d'inscription invalide`);
      return false;
    }
    if (!urlPattern.test(course.imageUrl)) {
      console.error(`❌ Cours ${index + 1}: URL d'image invalide`);
      return false;
    }
  });
  
  console.log(`✅ Toutes les URLs sont valides`);
  return true;
};

// Test 5: Vérifier les ratings
console.log('\n🧪 Test 5: Validation des ratings');
const testRatings = () => {
  courses.forEach((course, index) => {
    if (course.rating < 0 || course.rating > 5) {
      console.error(`❌ Cours ${index + 1}: Rating invalide (${course.rating})`);
      return false;
    }
  });
  
  console.log(`✅ Tous les ratings sont valides (0-5)`);
  return true;
};

// Exécuter tous les tests
const runAllTests = () => {
  console.log('🚀 Démarrage des tests de validation\n');
  
  const tests = [
    testCourseData,
    testCategories,
    testLevels,
    testUrls,
    testRatings
  ];
  
  let passedTests = 0;
  
  tests.forEach(test => {
    try {
      if (test()) {
        passedTests++;
      }
    } catch (error) {
      console.error(`❌ Erreur lors du test: ${error.message}`);
    }
  });
  
  console.log(`\n📊 Résultats: ${passedTests}/${tests.length} tests réussis`);
  
  if (passedTests === tests.length) {
    console.log('🎉 Tous les tests sont passés avec succès !');
  } else {
    console.log('⚠️ Certains tests ont échoué. Vérifiez les erreurs ci-dessus.');
  }
};

// Exporter pour utilisation dans d'autres fichiers
export { runAllTests };

// Exécuter automatiquement si ce fichier est lancé directement
if (typeof window === 'undefined') {
  runAllTests();
}
