# CoursLibre - Plateforme de Cours Gratuits avec Certificat

Une plateforme web moderne développée en React.js + Vite qui répertorie des cours gratuits avec certificat de completion.

## 🚀 Fonctionnalités

- **Parcourir les cours par catégorie** : Informatique, IA, Développement Web, Marketing Digital, Data Science, Design
- **Recherche avancée** : Recherche par titre, description ou compétences
- **Filtres multiples** : Par catégorie, niveau de difficulté et plateforme
- **Détails complets** : Informations détaillées sur chaque cours (durée, instructeur, compétences, etc.)
- **Inscription directe** : Redirection vers les plateformes d'inscription
- **Interface responsive** : Optimisée pour tous les appareils
- **Design moderne** : Interface utilisateur intuitive et attrayante

## 🛠️ Technologies utilisées

- **React 19** - Framework JavaScript
- **Vite** - Outil de build rapide
- **Lucide React** - Icônes modernes
- **CSS3** - Styling avec animations et gradients
- **React Router DOM** - Navigation (prêt pour extension future)

## 📦 Installation

1. Clonez le repository :
```bash
git clone <url-du-repo>
cd platform
```

2. Installez les dépendances :
```bash
npm install
```

3. Lancez le serveur de développement :
```bash
npm run dev
```

4. Ouvrez votre navigateur sur `http://localhost:5173`

## 🏗️ Structure du projet

```
src/
├── components/          # Composants React
│   ├── Header.jsx      # En-tête avec recherche
│   ├── FilterBar.jsx   # Barre de filtres
│   ├── CourseCard.jsx  # Carte de cours
│   ├── CourseList.jsx  # Liste des cours
│   ├── CourseDetail.jsx # Détails d'un cours
│   ├── StatsSection.jsx # Statistiques
│   └── Footer.jsx      # Pied de page
├── data/
│   └── courses.js      # Base de données des cours
├── App.jsx             # Composant principal
├── main.jsx           # Point d'entrée
└── styles/            # Fichiers CSS
```

## 📚 Cours disponibles

La plateforme inclut actuellement des cours de :
- **Harvard CS50** (Introduction à l'informatique)
- **Stanford Machine Learning** (Andrew Ng)
- **freeCodeCamp** (Développement Web Full-Stack)
- **Google Digital Garage** (Marketing Digital)
- **Kaggle Learn** (Data Science avec Python)
- **Interaction Design Foundation** (UX Design)

## 🔮 Fonctionnalités futures

- [ ] Système de connexion utilisateur
- [ ] Favoris et listes personnalisées
- [ ] Notifications de nouveaux cours
- [ ] Système de recommandations
- [ ] Commentaires et évaluations
- [ ] Progression des cours
- [ ] API backend pour la gestion des données

## 🤝 Contribution

Les contributions sont les bienvenues ! N'hésitez pas à :
1. Fork le projet
2. Créer une branche pour votre fonctionnalité
3. Commiter vos changements
4. Pousser vers la branche
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 📞 Contact

Pour toute question ou suggestion, contactez-nous à : <EMAIL>
