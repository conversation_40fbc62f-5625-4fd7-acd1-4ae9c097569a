.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;
}

.header-brand {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.brand-icon {
  width: 2rem;
  height: 2rem;
  color: #ffd700;
}

.brand-title {
  font-size: 1.8rem;
  font-weight: bold;
  margin: 0;
}

.brand-subtitle {
  font-size: 0.9rem;
  opacity: 0.9;
  margin-left: 0.5rem;
}

.header-search {
  flex: 1;
  max-width: 400px;
  margin: 0 2rem;
}

.search-container {
  position: relative;
  width: 100%;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.2rem;
  height: 1.2rem;
  color: #666;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  outline: none;
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;
}

.search-input:focus {
  background: white;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

.header-stats {
  display: flex;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.stat-icon {
  width: 1.2rem;
  height: 1.2rem;
  color: #ffd700;
}

.stat-text {
  font-size: 0.9rem;
  font-weight: 500;
}

@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    text-align: center;
  }
  
  .header-search {
    margin: 0;
    width: 100%;
    max-width: none;
  }
  
  .brand-subtitle {
    display: block;
    margin-left: 0;
    margin-top: 0.25rem;
  }
}
