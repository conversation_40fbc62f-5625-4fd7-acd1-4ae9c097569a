import { BookOpen, Search, Filter, Award } from 'lucide-react';
import './WelcomeSection.css';

const WelcomeSection = () => {
  return (
    <section className="welcome-section">
      <div className="welcome-container">
        <div className="welcome-content">
          <h1 className="welcome-title">
            Découvrez des milliers de cours gratuits avec certificat
          </h1>
          <p className="welcome-description">
            Apprenez de nouvelles compétences avec les meilleurs cours en ligne, 
            100% gratuits et certifiés par les plus grandes universités et entreprises du monde.
          </p>
          
          <div className="welcome-features">
            <div className="feature">
              <BookOpen className="feature-icon" />
              <span>Cours de qualité</span>
            </div>
            <div className="feature">
              <Award className="feature-icon" />
              <span>Certificats gratuits</span>
            </div>
            <div className="feature">
              <Search className="feature-icon" />
              <span>Recherche avancée</span>
            </div>
            <div className="feature">
              <Filter className="feature-icon" />
              <span>Filtres intelligents</span>
            </div>
          </div>
        </div>
        
        <div className="welcome-image">
          <div className="floating-card card-1">
            <BookOpen size={24} />
            <span>Informatique</span>
          </div>
          <div className="floating-card card-2">
            <Award size={24} />
            <span>Certificat</span>
          </div>
          <div className="floating-card card-3">
            <Search size={24} />
            <span>IA & ML</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WelcomeSection;
