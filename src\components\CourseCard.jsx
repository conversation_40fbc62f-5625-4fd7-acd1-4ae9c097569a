import { Clock, Users, Star, Award, ExternalLink, Calendar } from 'lucide-react';
import './CourseCard.css';

const CourseCard = ({ course, onClick }) => {
  const handleEnrollClick = (e) => {
    e.stopPropagation();
    window.open(course.enrollmentUrl, '_blank');
  };

  const getLevelColor = (level) => {
    switch (level) {
      case 'Débutant': return '#4CAF50';
      case 'Intermédiaire': return '#FF9800';
      case 'Avancé': return '#F44336';
      default: return '#2196F3';
    }
  };

  return (
    <div className="course-card" onClick={onClick}>
      <div className="course-image">
        <img src={course.imageUrl} alt={course.title} />
        <div className="course-level" style={{ backgroundColor: getLevelColor(course.level) }}>
          {course.level}
        </div>
        {course.certificate && (
          <div className="certificate-badge">
            <Award size={16} />
          </div>
        )}
      </div>
      
      <div className="course-content">
        <div className="course-header">
          <h3 className="course-title">{course.title}</h3>
          <span className="course-platform">{course.platform}</span>
        </div>
        
        <p className="course-description">{course.description}</p>
        
        <div className="course-meta">
          <div className="meta-item">
            <Clock size={16} />
            <span>{course.duration}</span>
          </div>
          <div className="meta-item">
            <Users size={16} />
            <span>{course.students.toLocaleString()} étudiants</span>
          </div>
          <div className="meta-item">
            <Star size={16} />
            <span>{course.rating}/5</span>
          </div>
        </div>
        
        <div className="course-skills">
          {course.skills.slice(0, 3).map((skill, index) => (
            <span key={index} className="skill-tag">{skill}</span>
          ))}
          {course.skills.length > 3 && (
            <span className="skill-tag more">+{course.skills.length - 3}</span>
          )}
        </div>
        
        <div className="course-footer">
          <div className="course-info">
            <div className="info-item">
              <Calendar size={14} />
              <span>{course.startDate}</span>
            </div>
            <div className="info-item">
              <Clock size={14} />
              <span>{course.effort}</span>
            </div>
          </div>
          
          <button 
            className="enroll-button"
            onClick={handleEnrollClick}
          >
            <ExternalLink size={16} />
            S'inscrire
          </button>
        </div>
      </div>
    </div>
  );
};

export default CourseCard;
