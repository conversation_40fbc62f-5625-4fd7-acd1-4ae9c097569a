.category-showcase {
  background: #f8f9fa;
  padding: 4rem 0;
  margin: 2rem 0;
}

.category-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.category-header {
  text-align: center;
  margin-bottom: 3rem;
}

.category-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 1rem;
}

.category-header p {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.category-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.category-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.category-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
  transition: transform 0.3s ease;
}

.category-card:hover .category-icon {
  transform: scale(1.1);
}

.category-content {
  flex: 1;
}

.category-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 0.75rem 0;
}

.category-stats {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.course-count {
  color: #666;
  font-size: 0.9rem;
  background: #f0f0f0;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-weight: 500;
}

.avg-rating {
  color: #ffa500;
  font-size: 0.9rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.category-arrow {
  font-size: 1.5rem;
  color: #667eea;
  font-weight: bold;
  transition: transform 0.3s ease;
}

.category-card:hover .category-arrow {
  transform: translateX(4px);
}

.category-footer {
  text-align: center;
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.category-footer p {
  font-size: 1.1rem;
  color: #333;
  margin: 0;
}

.category-footer strong {
  color: #667eea;
}

@media (max-width: 768px) {
  .category-header h2 {
    font-size: 2rem;
  }
  
  .category-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .category-card {
    padding: 1.5rem;
    gap: 1rem;
  }
  
  .category-icon {
    width: 60px;
    height: 60px;
  }
  
  .category-name {
    font-size: 1.1rem;
  }
  
  .category-stats {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
