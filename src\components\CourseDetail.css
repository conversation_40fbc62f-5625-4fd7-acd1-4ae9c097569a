.course-detail {
  min-height: 100vh;
  background: #f8f9fa;
}

.course-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: #667eea;
  font-size: 1rem;
  cursor: pointer;
  margin-bottom: 2rem;
  padding: 0.5rem 0;
  transition: color 0.3s ease;
}

.back-button:hover {
  color: #5a67d8;
}

.course-detail-header {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.course-detail-image {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  height: 250px;
}

.course-detail-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-level-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.certificate-badge {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  background: #ffd700;
  color: #333;
  padding: 0.75rem 1rem;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  font-size: 0.9rem;
}

.course-detail-info {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.course-detail-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.course-platform {
  color: #667eea;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 2rem;
}

.course-stats {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
  font-size: 1rem;
}

.stat-icon {
  color: #999;
}

.enroll-button-large {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 30px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  font-size: 1.1rem;
  align-self: flex-start;
}

.enroll-button-large:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.course-detail-content {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 3rem;
}

.course-main-content {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.course-section {
  margin-bottom: 3rem;
}

.course-section:last-child {
  margin-bottom: 0;
}

.course-section h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 1rem;
}

.course-section p {
  color: #666;
  line-height: 1.7;
  font-size: 1rem;
}

.skills-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.skill-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.language-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.language-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #666;
}

.language-item svg {
  color: #999;
}

.course-sidebar {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.course-info-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.course-info-card h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 1.5rem;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item svg {
  color: #999;
  margin-top: 0.25rem;
  flex-shrink: 0;
}

.info-item div {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-item strong {
  color: #333;
  font-weight: 600;
}

.info-item span {
  color: #666;
}

.certificate-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: #fff3cd;
  color: #856404;
  padding: 1rem;
  border-radius: 12px;
  margin-top: 1rem;
  font-weight: 600;
}

.certificate-info svg {
  color: #ffd700;
}

@media (max-width: 1024px) {
  .course-detail-header {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .course-detail-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .course-detail-container {
    padding: 1rem;
  }
  
  .course-detail-header {
    padding: 1.5rem;
  }
  
  .course-detail-title {
    font-size: 2rem;
  }
  
  .course-stats {
    flex-direction: column;
    gap: 1rem;
  }
  
  .course-main-content,
  .course-info-card {
    padding: 1.5rem;
  }
}
