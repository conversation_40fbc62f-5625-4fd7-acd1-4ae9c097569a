import { 
  Code, 
  Brain, 
  Palette, 
  TrendingUp, 
  Shield, 
  Smartphone,
  Camera,
  Music,
  Briefcase,
  Globe
} from 'lucide-react';
import { courses, categories } from '../data/courses';
import './CategoryShowcase.css';

const CategoryShowcase = ({ onCategorySelect }) => {
  // Icônes pour chaque catégorie
  const categoryIcons = {
    "Informatique": Code,
    "Intelligence Artificielle": Brain,
    "Design": <PERSON>lette,
    "Marketing Digital": TrendingUp,
    "Cybersécurité": Shield,
    "Développement Mobile": Smartphone,
    "Photographie": Camera,
    "Musique": Music,
    "Entrepreneuriat": Briefcase,
    "Langues": Globe
  };

  // Calculer le nombre de cours par catégorie
  const categoryStats = categories
    .filter(cat => cat !== 'Tous')
    .map(category => {
      const courseCount = courses.filter(course => course.category === category).length;
      const avgRating = courses
        .filter(course => course.category === category)
        .reduce((sum, course, _, arr) => sum + course.rating / arr.length, 0);
      
      return {
        name: category,
        courseCount,
        avgRating: avgRating.toFixed(1),
        icon: categoryIcons[category] || Code
      };
    })
    .filter(cat => cat.courseCount > 0)
    .sort((a, b) => b.courseCount - a.courseCount)
    .slice(0, 10);

  const handleCategoryClick = (categoryName) => {
    onCategorySelect(categoryName);
  };

  return (
    <section className="category-showcase">
      <div className="category-container">
        <div className="category-header">
          <h2>Explorez par catégorie</h2>
          <p>Découvrez nos domaines d'expertise les plus populaires</p>
        </div>

        <div className="category-grid">
          {categoryStats.map((category, index) => {
            const IconComponent = category.icon;
            return (
              <div 
                key={index} 
                className="category-card"
                onClick={() => handleCategoryClick(category.name)}
              >
                <div className="category-icon">
                  <IconComponent size={32} />
                </div>
                <div className="category-content">
                  <h3 className="category-name">{category.name}</h3>
                  <div className="category-stats">
                    <span className="course-count">{category.courseCount} cours</span>
                    <span className="avg-rating">★ {category.avgRating}</span>
                  </div>
                </div>
                <div className="category-arrow">→</div>
              </div>
            );
          })}
        </div>

        <div className="category-footer">
          <p>
            Plus de <strong>{courses.length} cours</strong> disponibles dans 
            <strong> {categories.length - 1} catégories</strong> différentes
          </p>
        </div>
      </div>
    </section>
  );
};

export default CategoryShowcase;
